{"name": "demand-supply-reconciliation-system", "version": "1.0.0", "description": "Smart Manufacturing Demand-Supply Reconciliation System with GenAI Auto-Recommendations", "repository": "<Add your repository here>", "license": "UNLICENSED", "private": true, "dependencies": {"@sap/cds": "^8", "@sap/cds-hana": "^2", "@sap/cds-dk": "^8", "@sap/xssec": "^3", "@sap/hdi-deploy": "^4", "express": "^4", "passport": "^0.6.0", "axios": "^1.6.0", "uuid": "^9.0.0"}, "devDependencies": {"@cap-js/sqlite": "^1", "@sap/ux-specification": "^1.108.0", "rimraf": "^5.0.0"}, "scripts": {"start": "cds-serve", "watch": "cds watch", "build": "cds build", "deploy": "cds deploy", "test": "jest", "undeploy": "cf undeploy demand-supply-reconciliation-system --delete-services --delete-service-keys"}, "cds": {"requires": {"db": {"kind": "hana-cloud", "[development]": {"kind": "sqlite", "credentials": {"url": "db.sqlite"}}}, "auth": {"[development]": {"kind": "mocked"}, "[production]": {"kind": "xsuaa"}}}, "hana": {"deploy-format": "hdbtable"}, "build": {"target": ".", "tasks": [{"for": "hana", "dest": "../db"}, {"for": "node-cf"}]}}, "sapux": ["app/reconciliation-dashboard"]}