{"name": "reconciliation-dashboard", "version": "0.0.1", "description": "Demand-Supply Reconciliation Dashboard", "keywords": ["ui5", "openui5", "sapui5"], "main": "webapp/index.html", "dependencies": {}, "devDependencies": {"@ui5/cli": "^3.0.0", "@sap/ux-ui5-tooling": "1", "@sap/ux-specification": "UI5-1.108"}, "scripts": {"start": "fiori run --open \"test/flpSandbox.html?sap-ui-xx-viewCache=false#reconciliationdashboard-display\"", "start-local": "fiori run --config ./ui5-local.yaml --open \"test/flpSandbox.html?sap-ui-xx-viewCache=false#reconciliationdashboard-display\"", "build": "ui5 build --config=ui5.yaml --clean-dest --dest dist", "deploy": "fiori verify", "deploy-config": "fiori add deploy-config", "start-noflp": "fiori run --open \"index.html?sap-ui-xx-viewCache=false\"", "start-mock": "fiori run --config ./ui5-mock.yaml --open \"test/flpSandbox.html?sap-ui-xx-viewCache=false#reconciliationdashboard-display\"", "start-variants-management": "fiori run --open \"preview.html?sap-ui-xx-viewCache=false&fiori-tools-rta-mode=true&sap-ui-rta-skip-flex-validation=true#preview-app\"", "unit-tests": "fiori run --config ./ui5-mock.yaml --open 'test/unit/unitTests.qunit.html'", "int-tests": "fiori run --config ./ui5-mock.yaml --open 'test/integration/opaTests.qunit.html'", "build:cf": "ui5 build preload --clean-dest --config ui5-deploy.yaml --include-task=generateCachebusterInfo"}, "sapuxLayer": "CUSTOMER_BASE", "ui5": {"dependencies": ["@sap/ux-ui5-tooling", "@ui5/logger", "@ui5/fs", "@ui5/builder", "@sap/ux-specification"]}}