sap.ui.define([
    "sap/ui/core/mvc/Controller",
    "sap/ui/model/json/JSONModel",
    "sap/m/MessageToast",
    "sap/m/MessageBox"
], function (Controller, JSONModel, MessageToast, MessageBox) {
    "use strict";

    return Controller.extend("com.sap.manufacturing.reconciliation.dashboard.controller.Dashboard", {

        onInit: function () {
            // Initialize dashboard model with KPI data
            var oDashboardModel = new JSONModel({
                totalReconciliations: 0,
                pendingReconciliations: 0,
                aiSuccessRate: 0,
                totalShortfall: 0,
                refreshing: false
            });
            this.getView().setModel(oDashboardModel, "dashboard");
            
            // Load initial data
            this._loadDashboardData();
        },

        _loadDashboardData: function () {
            var oModel = this.getOwnerComponent().getModel();
            var oDashboardModel = this.getView().getModel("dashboard");
            
            oDashboardModel.setProperty("/refreshing", true);
            
            // Load reconciliation results to calculate KPIs
            var oBinding = oModel.bindList("/ReconciliationResults", null, null, null, {
                $expand: "plant,product"
            });
            
            oBinding.requestContexts().then(function (aContexts) {
                var iTotalReconciliations = aContexts.length;
                var iPendingReconciliations = 0;
                var iTotalShortfall = 0;
                var iTotalSurplus = 0;
                
                aContexts.forEach(function (oContext) {
                    var oData = oContext.getObject();
                    if (oData.status === "PENDING") {
                        iPendingReconciliations++;
                    }
                    if (oData.shortfall) {
                        iTotalShortfall += parseFloat(oData.shortfall);
                    }
                    if (oData.surplus) {
                        iTotalSurplus += parseFloat(oData.surplus);
                    }
                });
                
                // Update dashboard KPIs
                oDashboardModel.setData({
                    totalReconciliations: iTotalReconciliations,
                    pendingReconciliations: iPendingReconciliations,
                    aiSuccessRate: 85.5, // Mock data for now
                    totalShortfall: iTotalShortfall,
                    totalSurplus: iTotalSurplus,
                    refreshing: false
                });
                
            }.bind(this)).catch(function (oError) {
                oDashboardModel.setProperty("/refreshing", false);
                MessageToast.show("Error loading dashboard data: " + oError.message);
            });
        },

        onKPIPress: function (oEvent) {
            var sKPIId = oEvent.getSource().getId();
            MessageToast.show("KPI pressed: " + sKPIId);
            // Navigate to detailed analytics view
        },

        onTriggerReconciliation: function () {
            MessageBox.confirm(
                "This will trigger reconciliation for all pending demand-supply mismatches. Continue?",
                {
                    title: "Trigger Reconciliation",
                    onClose: function (sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._triggerReconciliation();
                        }
                    }.bind(this)
                }
            );
        },

        _triggerReconciliation: function () {
            var oModel = this.getOwnerComponent().getModel();
            
            // Call the reconciliation service action
            var oBinding = oModel.bindContext("/ReconciliationResults");
            oBinding.invoke("triggerReconciliation").then(function () {
                MessageToast.show("Reconciliation process started successfully");
                this._loadDashboardData(); // Refresh data
            }.bind(this)).catch(function (oError) {
                MessageBox.error("Failed to trigger reconciliation: " + oError.message);
            });
        },

        onGenerateAIRecommendations: function () {
            MessageBox.confirm(
                "This will generate AI recommendations for all pending reconciliations. Continue?",
                {
                    title: "Generate AI Recommendations",
                    onClose: function (sAction) {
                        if (sAction === MessageBox.Action.OK) {
                            this._generateAIRecommendations();
                        }
                    }.bind(this)
                }
            );
        },

        _generateAIRecommendations: function () {
            var oModel = this.getOwnerComponent().getModel();
            
            // Get pending reconciliations and generate AI recommendations
            var oBinding = oModel.bindList("/ReconciliationResults", null, null, 
                [new sap.ui.model.Filter("status", sap.ui.model.FilterOperator.EQ, "PENDING")]);
            
            oBinding.requestContexts().then(function (aContexts) {
                var aPromises = [];
                
                aContexts.forEach(function (oContext) {
                    var oActionBinding = oContext.getModel().bindContext("generateAIRecommendation(...)", oContext);
                    aPromises.push(oActionBinding.invoke());
                });
                
                Promise.all(aPromises).then(function () {
                    MessageToast.show("AI recommendations generated successfully");
                    this._loadDashboardData(); // Refresh data
                }.bind(this)).catch(function (oError) {
                    MessageBox.error("Failed to generate AI recommendations: " + oError.message);
                });
                
            }.bind(this)).catch(function (oError) {
                MessageBox.error("Failed to load pending reconciliations: " + oError.message);
            });
        },

        onViewAnalytics: function () {
            this.getRouter().navTo("RouteAnalytics");
        },

        onManageSettings: function () {
            MessageToast.show("Settings management coming soon");
        },

        onReconciliationSelect: function (oEvent) {
            var oSelectedItem = oEvent.getParameter("listItem");
            var oContext = oSelectedItem.getBindingContext();
            var sObjectId = oContext.getProperty("ID");
            
            this.getRouter().navTo("RouteReconciliation", {
                objectId: sObjectId
            });
        },

        onReconciliationPress: function (oEvent) {
            var oContext = oEvent.getSource().getBindingContext();
            var sObjectId = oContext.getProperty("ID");
            
            this.getRouter().navTo("RouteReconciliation", {
                objectId: sObjectId
            });
        },

        onViewDetails: function (oEvent) {
            var oContext = oEvent.getSource().getBindingContext();
            var sObjectId = oContext.getProperty("ID");
            
            this.getRouter().navTo("RouteReconciliation", {
                objectId: sObjectId
            });
        },

        onGenerateRecommendation: function (oEvent) {
            var oContext = oEvent.getSource().getBindingContext();
            var oActionBinding = oContext.getModel().bindContext("generateAIRecommendation(...)", oContext);
            
            oActionBinding.invoke().then(function () {
                MessageToast.show("AI recommendation generated successfully");
                this._loadDashboardData(); // Refresh data
            }.bind(this)).catch(function (oError) {
                MessageBox.error("Failed to generate AI recommendation: " + oError.message);
            });
        },

        onSearch: function (oEvent) {
            var sQuery = oEvent.getParameter("newValue");
            var oTable = this.byId("reconciliationsTable");
            var oBinding = oTable.getBinding("items");
            
            if (sQuery && sQuery.length > 0) {
                var aFilters = [
                    new sap.ui.model.Filter("plant/plantName", sap.ui.model.FilterOperator.Contains, sQuery),
                    new sap.ui.model.Filter("product/productName", sap.ui.model.FilterOperator.Contains, sQuery),
                    new sap.ui.model.Filter("status", sap.ui.model.FilterOperator.Contains, sQuery)
                ];
                var oFilter = new sap.ui.model.Filter({
                    filters: aFilters,
                    and: false
                });
                oBinding.filter(oFilter);
            } else {
                oBinding.filter([]);
            }
        },

        getRouter: function () {
            return this.getOwnerComponent().getRouter();
        },

        getResourceBundle: function () {
            return this.getOwnerComponent().getModel("i18n").getResourceBundle();
        }
    });
});
