sap.ui.define([
    "sap/ui/core/mvc/Controller"
], function (Controller) {
    "use strict";

    return Controller.extend("com.sap.manufacturing.reconciliation.dashboard.controller.App", {

        onInit: function () {
            // Apply content density mode to root view
            this.getView().addStyleClass(this.getOwnerComponent().getContentDensityClass());
        },

        onRefresh: function () {
            // Refresh all models
            this.getOwnerComponent().getModel().refresh();
            this.getOwnerComponent().getModel("analytics").refresh();
            
            sap.m.MessageToast.show(this.getResourceBundle().getText("refreshMessage"));
        },

        onSettings: function () {
            sap.m.MessageToast.show(this.getResourceBundle().getText("settingsMessage"));
        },

        getResourceBundle: function () {
            return this.getOwnerComponent().getModel("i18n").getResourceBundle();
        }
    });
});
