/* Custom styles for Reconciliation Dashboard */

.kpiCard {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    background-color: #fafafa;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.kpiCard:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.sapUiMediumMargin {
    margin: 1rem;
}

.sapUiResponsiveMargin {
    margin: 0.5rem;
}

/* Dashboard specific styles */
.dashboardPage {
    background-color: #f7f7f7;
}

/* Table customizations */
.reconciliationsTable .sapMListTblRow:hover {
    background-color: #f0f8ff;
}

/* Button customizations */
.quickActionButton {
    min-width: 150px;
    margin: 0.25rem;
}

/* Status indicators */
.statusPending {
    color: #e78c07;
}

.statusResolved {
    color: #2b7d2b;
}

.statusEscalated {
    color: #bb0000;
}

/* Priority indicators */
.priorityHigh {
    color: #bb0000;
    font-weight: bold;
}

.priorityMedium {
    color: #e78c07;
}

.priorityLow {
    color: #2b7d2b;
}

/* Responsive design */
@media (max-width: 768px) {
    .kpiCard {
        width: 100% !important;
        margin-bottom: 1rem;
    }
    
    .quickActionButton {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
