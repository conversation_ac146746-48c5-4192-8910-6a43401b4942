{"_version": "1.58.0", "sap.app": {"id": "com.sap.manufacturing.reconciliation.dashboard", "type": "application", "i18n": "i18n/i18n.properties", "applicationVersion": {"version": "0.0.1"}, "title": "{{appTitle}}", "description": "{{appDescription}}", "resources": "resources.json", "sourceTemplate": {"id": "@sap/generator-fiori:basic", "version": "1.11.4", "toolsId": "b5b7c4b0-9d8a-4c8e-8f7e-6d5c4b3a2f1e"}, "dataSources": {"mainService": {"uri": "/reconciliation/", "type": "OData", "settings": {"annotations": [], "localUri": "localService/metadata.xml", "odataVersion": "4.0"}}, "analyticsService": {"uri": "/analytics/", "type": "OData", "settings": {"annotations": [], "localUri": "localService/analytics-metadata.xml", "odataVersion": "4.0"}}}}, "sap.ui": {"technology": "UI5", "icons": {"icon": "", "favIcon": "", "phone": "", "phone@2": "", "tablet": "", "tablet@2": ""}, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"flexEnabled": true, "dependencies": {"minUI5Version": "1.108.0", "libs": {"sap.m": {}, "sap.ui.core": {}, "sap.f": {}, "sap.suite.ui.commons": {}, "sap.ui.comp": {}, "sap.ui.generic.app": {}, "sap.ui.table": {}, "sap.ushell": {}}}, "models": {"i18n": {"dataSource": "i18n", "type": "sap.ui.model.resource.ResourceModel", "settings": {"bundleName": "com.sap.manufacturing.reconciliation.dashboard.i18n.i18n"}}, "": {"dataSource": "mainService", "type": "sap.ui.model.odata.v4.ODataModel", "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true}}, "analytics": {"dataSource": "analyticsService", "type": "sap.ui.model.odata.v4.ODataModel", "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true}}}, "resources": {"css": [{"uri": "css/style.css"}]}, "routing": {"config": {"routerClass": "sap.m.routing.Router", "viewType": "XML", "async": true, "viewPath": "com.sap.manufacturing.reconciliation.dashboard.view", "controlAggregation": "pages", "controlId": "app", "clearControlAggregation": false}, "routes": [{"name": "RouteDashboard", "pattern": ":?query:", "target": ["TargetDashboard"]}, {"name": "RouteReconciliation", "pattern": "reconciliation/{objectId}:?query:", "target": ["TargetReconciliation"]}, {"name": "RouteAnalytics", "pattern": "analytics:?query:", "target": ["TargetAnalytics"]}], "targets": {"TargetDashboard": {"viewType": "XML", "transition": "slide", "clearControlAggregation": false, "viewId": "Dashboard", "viewName": "Dashboard"}, "TargetReconciliation": {"viewType": "XML", "transition": "slide", "clearControlAggregation": false, "viewId": "Reconciliation", "viewName": "Reconciliation"}, "TargetAnalytics": {"viewType": "XML", "transition": "slide", "clearControlAggregation": false, "viewId": "Analytics", "viewName": "Analytics"}}}, "rootView": {"viewName": "com.sap.manufacturing.reconciliation.dashboard.view.App", "type": "XML", "async": true, "id": "App"}}, "sap.cloud": {"public": true, "service": "com.sap.manufacturing.reconciliation.dashboard"}}