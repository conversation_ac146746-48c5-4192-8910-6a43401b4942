<mvc:View
    controllerName="com.sap.manufacturing.reconciliation.dashboard.controller.Dashboard"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns:core="sap.ui.core"
    xmlns:suite="sap.suite.ui.commons"
    xmlns="sap.m">
    
    <Page id="dashboardPage" title="{i18n>dashboardTitle}" showNavButton="false">
        <content>
            <VBox id="_IDGenVBox1" class="sapUiMediumMargin">
                
                <!-- KPI Cards Section -->
                <Panel id="_IDGenPanel" headerText="{i18n>kpiTitle}" class="sapUiResponsiveMargin" width="auto">
                    <content>
                        <HBox id="_IDGenHBox" class="sapUiMediumMargin" justifyContent="SpaceAround" wrap="Wrap">
                            
                            <!-- Total Reconciliations Card -->
                            <VBox id="_IDGenVBox2" class="sapUiMediumMargin kpiCard" width="200px">
                                <suite:NumericTile
                                    id="totalReconciliationsTile"
                                    headerText="{i18n>totalReconciliations}"
                                    subHeaderText="{i18n>thisMonth}"
                                    valueColor="Good"
                                    indicator="Up"
                                    value="{dashboard>/totalReconciliations}"
                                    size="M"
                                    press="onKPIPress"/>
                            </VBox>
                            
                            <!-- Pending Reconciliations Card -->
                            <VBox id="_IDGenVBox3" class="sapUiMediumMargin kpiCard" width="200px">
                                <suite:NumericTile
                                    id="pendingReconciliationsTile"
                                    headerText="{i18n>pendingReconciliations}"
                                    subHeaderText="{i18n>requiresAction}"
                                    valueColor="Critical"
                                    indicator="None"
                                    value="{dashboard>/pendingReconciliations}"
                                    size="M"
                                    press="onKPIPress"/>
                            </VBox>
                            
                            <!-- AI Success Rate Card -->
                            <VBox id="_IDGenVBox4" class="sapUiMediumMargin kpiCard" width="200px">
                                <suite:NumericTile
                                    id="aiSuccessRateTile"
                                    headerText="{i18n>aiSuccessRate}"
                                    subHeaderText="{i18n>implementationRate}"
                                    valueColor="Good"
                                    indicator="Up"
                                    value="{dashboard>/aiSuccessRate}"
                                    size="M"
                                    press="onKPIPress"/>
                            </VBox>
                            
                            <!-- Total Shortfall Card -->
                            <VBox id="_IDGenVBox5" class="sapUiMediumMargin kpiCard" width="200px">
                                <suite:NumericTile
                                    id="totalShortfallTile"
                                    headerText="{i18n>totalShortfall}"
                                    subHeaderText="{i18n>unitsShort}"
                                    valueColor="Error"
                                    indicator="Down"
                                    value="{dashboard>/totalShortfall}"
                                    size="M"
                                    press="onKPIPress"/>
                            </VBox>
                            
                        </HBox>
                    </content>
                </Panel>
                
                <!-- Quick Actions Section -->
                <Panel id="_IDGenPanel1" headerText="{i18n>quickActions}" class="sapUiResponsiveMargin" width="auto">
                    <content>
                        <HBox id="_IDGenHBox1" class="sapUiMediumMargin" justifyContent="SpaceAround" wrap="Wrap">
                            <Button id="_IDGenButton" 
                                text="{i18n>triggerReconciliation}" 
                                icon="sap-icon://process"
                                type="Emphasized"
                                press="onTriggerReconciliation"
                                class="sapUiMediumMargin"/>
                            <Button id="_IDGenButton1" 
                                text="{i18n>generateAIRecommendations}" 
                                icon="sap-icon://lightbulb"
                                type="Default"
                                press="onGenerateAIRecommendations"
                                class="sapUiMediumMargin"/>
                            <Button id="_IDGenButton2" 
                                text="{i18n>viewAnalytics}" 
                                icon="sap-icon://bar-chart"
                                type="Default"
                                press="onViewAnalytics"
                                class="sapUiMediumMargin"/>
                            <Button id="_IDGenButton3" 
                                text="{i18n>manageSettings}" 
                                icon="sap-icon://action-settings"
                                type="Default"
                                press="onManageSettings"
                                class="sapUiMediumMargin"/>
                        </HBox>
                    </content>
                </Panel>
                
                <!-- Recent Reconciliations Table -->
                <Panel id="_IDGenPanel2" headerText="{i18n>recentReconciliations}" class="sapUiResponsiveMargin" width="auto">
                    <content>
                        <Table 
                            id="reconciliationsTable"
                            items="{/ReconciliationResults}"
                            growing="true"
                            growingThreshold="20"
                            mode="SingleSelection"
                            selectionChange="onReconciliationSelect">
                            
                            <headerToolbar>
                                <Toolbar id="_IDGenToolbar">
                                    <Title id="_IDGenTitle" text="{i18n>reconciliationResults}" level="H2"/>
                                    <ToolbarSpacer id="_IDGenToolbarSpacer"/>
                                    <SearchField id="_IDGenSearchField" 
                                        search="onSearch" 
                                        width="20rem"
                                        placeholder="{i18n>searchPlaceholder}"/>
                                </Toolbar>
                            </headerToolbar>
                            
                            <columns>
                                <Column id="_IDGenColumn" width="12rem">
                                    <Text id="_IDGenText" text="{i18n>reconciliationDate}"/>
                                </Column>
                                <Column id="_IDGenColumn1" width="10rem">
                                    <Text id="_IDGenText1" text="{i18n>plant}"/>
                                </Column>
                                <Column id="_IDGenColumn2" width="12rem">
                                    <Text id="_IDGenText2" text="{i18n>product}"/>
                                </Column>
                                <Column id="_IDGenColumn3" width="8rem">
                                    <Text id="_IDGenText3" text="{i18n>shortfall}"/>
                                </Column>
                                <Column id="_IDGenColumn4" width="8rem">
                                    <Text id="_IDGenText4" text="{i18n>surplus}"/>
                                </Column>
                                <Column id="_IDGenColumn5" width="8rem">
                                    <Text id="_IDGenText5" text="{i18n>status}"/>
                                </Column>
                                <Column id="_IDGenColumn6" width="8rem">
                                    <Text id="_IDGenText6" text="{i18n>priority}"/>
                                </Column>
                                <Column id="_IDGenColumn7" width="10rem">
                                    <Text id="_IDGenText7" text="{i18n>actions}"/>
                                </Column>
                            </columns>
                            
                            <items>
                                <ColumnListItem id="_IDGenColumnListItem" press="onReconciliationPress">
                                    <cells>
                                        <Text id="_IDGenText8" text="{reconciliationDate}"/>
                                        <Text id="_IDGenText9" text="{plant/plantName}"/>
                                        <Text id="_IDGenText10" text="{product/productName}"/>
                                        <ObjectNumber id="shortfallNumber" number="{shortfall}"/>
                                        <ObjectNumber id="surplusNumber" number="{surplus}"/>
                                        <ObjectStatus id="statusObject" text="{status}"/>
                                        <ObjectStatus id="priorityObject" text="{priority}"/>
                                        <HBox id="actionBox">
                                            <Button
                                                id="viewDetailsBtn"
                                                icon="sap-icon://detail-view"
                                                type="Transparent"
                                                tooltip="{i18n>viewDetails}"
                                                press="onViewDetails"/>
                                            <Button
                                                id="generateRecommendationBtn"
                                                icon="sap-icon://lightbulb"
                                                type="Transparent"
                                                tooltip="{i18n>generateRecommendation}"
                                                press="onGenerateRecommendation"/>
                                        </HBox>
                                    </cells>
                                </ColumnListItem>
                            </items>
                        </Table>
                    </content>
                </Panel>
                
            </VBox>
        </content>
    </Page>
</mvc:View>
