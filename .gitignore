# CAP
_out
*.db
connection.properties
default-*.json
gen/
node_modules/
target/

# MTA
.mta/
mta_archives/

# Other
.DS_Store
*.orig
*.log
*.tgz
*.tar.gz

# IDEs
.vscode/
.idea/
*.swp
*.swo

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Flask
instance/
.webassets-cache

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# SAP specific
fiori-tools-proxy.middleware.js
fiori-tools-appreload.middleware.js
.sap/
