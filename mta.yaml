_schema-version: "3.1"
ID: demand-supply-reconciliation-system
description: Smart Manufacturing Demand-Supply Reconciliation System
version: 1.0.0
modules:
  - name: demand-supply-reconciliation-system-srv
    type: nodejs
    path: gen/srv
    requires:
      - name: demand-supply-reconciliation-system-db
      - name: demand-supply-reconciliation-system-xsuaa
      - name: demand-supply-reconciliation-system-destination
    provides:
      - name: srv-api
        properties:
          srv-url: ${default-url}
    parameters:
      buildpack: nodejs_buildpack
    build-parameters:
      builder: npm-ci
  - name: demand-supply-reconciliation-system-db-deployer
    type: hdb
    path: gen/db
    requires:
      - name: demand-supply-reconciliation-system-db
    parameters:
      buildpack: nodejs_buildpack
  - name: demand-supply-reconciliation-system-app-content
    type: com.sap.application.content
    path: .
    requires:
      - name: demand-supply-reconciliation-system-repo-host
        parameters:
          content-target: true
    build-parameters:
      build-result: resources
      requires:
        - artifacts:
            - reconciliationdashboard.zip
          name: reconciliationdashboard
          target-path: resources/
  - name: reconciliationdashboard
    type: html5
    path: app/reconciliation-dashboard
    build-parameters:
      build-result: dist
      builder: custom
      commands:
        - npm install
        - npm run build:cf
      supported-platforms: []
resources:
  - name: demand-supply-reconciliation-system-db
    type: com.sap.xs.hdi-container
    parameters:
      service: hana
      service-plan: hdi-shared
  - name: demand-supply-reconciliation-system-xsuaa
    type: com.sap.xs.uaa
    parameters:
      service: xsuaa
      service-plan: application
      path: ./xs-security.json
      config:
        xsappname: demand-supply-reconciliation-system-${org}-${space}
        tenant-mode: dedicated
  - name: demand-supply-reconciliation-system-repo-host
    type: org.cloudfoundry.managed-service
    parameters:
      service: html5-apps-repo
      service-name: demand-supply-reconciliation-system-html5-srv
      service-plan: app-host
  - name: demand-supply-reconciliation-system-destination
    type: org.cloudfoundry.managed-service
    parameters:
      config:
        HTML5Runtime_enabled: false
        init_data:
          instance:
            destinations:
              - Authentication: NoAuthentication
                Name: ui5
                ProxyType: Internet
                Type: HTTP
                URL: https://ui5.sap.com
            existing_destinations_policy: update
        version: 1.0.0
      service: destination
      service-name: demand-supply-reconciliation-system-destination-service
      service-plan: lite
parameters:
  enable-parallel-deployments: true
build-parameters:
  before-all:
    - builder: custom
      commands:
        - npx cds build --production
