#!/usr/bin/env python3
"""
GenAI Service for Demand-Supply Reconciliation System
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class GenAIService:
    def __init__(self):
        self.model = 'mistralai/Mistral-7B-Instruct-v0.1'

    def generate_recommendation(self, data):
        plant_name = data.get('plantName', 'Unknown Plant')
        product_name = data.get('productName', 'Unknown Product')
        demand_quantity = data.get('demandQuantity', 0)
        supply_quantity = data.get('supplyQuantity', 0)
        current_stock = data.get('currentStock', 0)
        shortage = demand_quantity - supply_quantity - current_stock

        if shortage > 1000:
            primary_action = 'EMERGENCY_ORDER'
            priority = 'HIGH'
        elif shortage > 100:
            primary_action = 'PROCURE'
            priority = 'MEDIUM'
        else:
            primary_action = 'REALLOCATE'
            priority = 'LOW'

        return {
            'id': f"rec_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'timestamp': datetime.now().isoformat(),
            'plantName': plant_name,
            'productName': product_name,
            'primaryAction': primary_action,
            'reasoning': f'Shortage analysis: {shortage} units',
            'priority': priority,
            'estimatedCost': f'${abs(shortage) * 15:.0f}',
            'timeline': '3-5 business days',
            'confidence': 0.85,
            'status': 'PENDING_APPROVAL'
        }

genai_service = GenAIService()

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'service': 'GenAI Reconciliation Service',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/recommendations/generate', methods=['POST'])
def generate_recommendation():
    try:
        if not request.is_json:
            return jsonify({'error': 'Request must be JSON'}), 400

        data = request.get_json()
        recommendation = genai_service.generate_recommendation(data)

        return jsonify({
            'success': True,
            'recommendation': recommendation
        })

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    logger.info(f"Starting GenAI Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=True)