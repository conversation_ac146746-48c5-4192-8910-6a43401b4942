using {com.sap.manufacturing.reconciliation as model} from '../db/schema';

namespace com.sap.manufacturing.reconciliation.service;

@path: '/analytics'
service AnalyticsService {

    // Historical Data
    @readonly
    entity ReconciliationHistory as projection on model.ReconciliationResults;

    @readonly
    entity ApprovalHistory as projection on model.ApprovalHistory;

    @readonly
    entity Plants as projection on model.Plants;

    @readonly
    entity Products as projection on model.Products;

    @readonly
    entity Vendors as projection on model.Vendors;

    @readonly
    entity Demands as projection on model.Demands;

    @readonly
    entity Supplies as projection on model.Supplies;

    @readonly
    entity Stocks as projection on model.Stocks;
}
