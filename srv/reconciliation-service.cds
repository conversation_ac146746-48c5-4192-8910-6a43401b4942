using {com.sap.manufacturing.reconciliation as model} from '../db/schema';

namespace com.sap.manufacturing.reconciliation.service;

@path: '/reconciliation'
service ReconciliationService {

    // Core Reconciliation Entities
    @odata.draft.enabled
    entity ReconciliationResults as projection on model.ReconciliationResults
        actions {
            action triggerReconciliation() returns ReconciliationResults;
            action approveReconciliation() returns ReconciliationResults;
            action rejectReconciliation() returns ReconciliationResults;
            action generateAIRecommendation() returns GenAIRecommendations;
        };

    @readonly
    entity GenAIRecommendations as projection on model.GenAIRecommendations
        actions {
            action approveRecommendation() returns GenAIRecommendations;
            action rejectRecommendation() returns GenAIRecommendations;
            action implementRecommendation() returns GenAIRecommendations;
        };

    // Workflow Entities
    @odata.draft.enabled
    entity ProcurementRequests as projection on model.ProcurementRequests
        actions {
            action approveProcurement() returns ProcurementRequests;
            action createPurchaseOrder() returns ProcurementRequests;
        };

    @odata.draft.enabled
    entity ProductionOrders as projection on model.ProductionOrders
        actions {
            action releaseProduction() returns ProductionOrders;
            action completeProduction() returns ProductionOrders;
        };

    @odata.draft.enabled
    entity StockReallocations as projection on model.StockReallocations
        actions {
            action executeReallocation() returns StockReallocations;
        };

    // Master Data (Read-only in this service)
    @readonly
    entity Plants as projection on model.Plants;

    @readonly
    entity Vendors as projection on model.Vendors;

    @readonly
    entity Products as projection on model.Products;

    @readonly
    entity Demands as projection on model.Demands;

    @readonly
    entity Supplies as projection on model.Supplies;

    @readonly
    entity Stocks as projection on model.Stocks;

    // Functions for complex operations
    function calculateReconciliation(
        plantID: UUID,
        productID: UUID,
        demandDate: Date
    ) returns {
        shortfall: Decimal(15,2);
        surplus: Decimal(15,2);
        recommendations: array of String;
    };

    function getReconciliationSummary(
        plantID: UUID,
        dateFrom: Date,
        dateTo: Date
    ) returns {
        totalReconciliations: Integer;
        pendingReconciliations: Integer;
        resolvedReconciliations: Integer;
        totalShortfall: Decimal(15,2);
        totalSurplus: Decimal(15,2);
        aiRecommendationsCount: Integer;
        implementedRecommendations: Integer;
    };

    function triggerAutoReconciliation(
        reconciliationID: UUID
    ) returns {
        success: Boolean;
        message: String;
        actionsTriggered: array of String;
    };
}
