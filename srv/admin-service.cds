using {com.sap.manufacturing.reconciliation as model} from '../db/schema';

namespace com.sap.manufacturing.reconciliation.service;

@path: '/admin'
service AdminService {

    // Master Data Management
    @odata.draft.enabled
    entity Plants as projection on model.Plants
        actions {
            action activatePlant() returns Plants;
            action deactivatePlant() returns Plants;
        };

    @odata.draft.enabled
    entity Vendors as projection on model.Vendors
        actions {
            action activateVendor() returns Vendors;
            action deactivateVendor() returns Vendors;
            action updateVendorRating(rating: Decimal(3,2)) returns Vendors;
        };

    @odata.draft.enabled
    entity Products as projection on model.Products
        actions {
            action activateProduct() returns Products;
            action deactivateProduct() returns Products;
            action updateStockLevels(
                minLevel: Decimal(15,2),
                maxLevel: Decimal(15,2)
            ) returns Products;
        };

    // Transactional Data Management
    @odata.draft.enabled
    entity Demands as projection on model.Demands
        actions {
            action fulfillDemand() returns Demands;
            action cancelDemand() returns Demands;
        };

    @odata.draft.enabled
    entity Supplies as projection on model.Supplies
        actions {
            action confirmSupply(confirmedQuantity: Decimal(15,2)) returns Supplies;
            action cancelSupply() returns Supplies;
        };

    @odata.draft.enabled
    entity Stocks as projection on model.Stocks
        actions {
            action updateStock(
                newQuantity: Decimal(15,2),
                reason: String
            ) returns Stocks;
            action reserveStock(quantity: Decimal(15,2)) returns Stocks;
            action releaseReservation(quantity: Decimal(15,2)) returns Stocks;
        };

    // System Configuration and Audit
    @readonly
    entity ApprovalHistory as projection on model.ApprovalHistory;

    // Bulk Operations
    function bulkUpdateStocks(
        updates: array of {
            stockID: UUID;
            newQuantity: Decimal(15,2);
            reason: String;
        }
    ) returns {
        success: Boolean;
        updatedCount: Integer;
        errors: array of String;
    };

    function bulkCreateDemands(
        demands: array of {
            plantID: UUID;
            productID: UUID;
            demandDate: Date;
            requiredQuantity: Decimal(15,2);
            priority: String;
            customerOrder: String;
            dueDate: Date;
        }
    ) returns {
        success: Boolean;
        createdCount: Integer;
        errors: array of String;
    };

    function bulkCreateSupplies(
        supplies: array of {
            plantID: UUID;
            vendorID: UUID;
            productID: UUID;
            supplyDate: Date;
            availableQuantity: Decimal(15,2);
            purchaseOrder: String;
            deliveryDate: Date;
        }
    ) returns {
        success: Boolean;
        createdCount: Integer;
        errors: array of String;
    };

    // Data Import/Export Functions
    function exportMasterData(
        entityType: String, // 'PLANTS', 'VENDORS', 'PRODUCTS'
        format: String // 'CSV', 'JSON'
    ) returns {
        success: Boolean;
        downloadUrl: String;
        recordCount: Integer;
    };

    function importMasterData(
        entityType: String,
        data: LargeString,
        format: String,
        validateOnly: Boolean
    ) returns {
        success: Boolean;
        validRecords: Integer;
        invalidRecords: Integer;
        errors: array of {
            row: Integer;
            field: String;
            message: String;
        };
    };

    // System Health and Monitoring
    function getSystemHealth() returns {
        status: String; // 'HEALTHY', 'WARNING', 'CRITICAL'
        checks: array of {
            component: String;
            status: String;
            message: String;
            lastChecked: Timestamp;
        };
        recommendations: array of String;
    };

    function cleanupOldData(
        entityType: String,
        olderThanDays: Integer,
        dryRun: Boolean
    ) returns {
        success: Boolean;
        recordsToDelete: Integer;
        recordsDeleted: Integer;
        errors: array of String;
    };
}
