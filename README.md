# Demand-Supply Reconciliation System with GenAI Auto-Recommendations

## 🎯 Project Overview
A full-stack smart manufacturing application that monitors real-time demand and supply data, identifies mismatches, and uses GenAI to suggest intelligent resolutions.

## 🏗️ Architecture
- **Frontend**: SAP UI5 (Fiori-style dashboard)
- **Backend**: SAP CAP (Cloud Application Programming Model, Node.js)
- **Database**: SAP HANA Cloud (HDI container)
- **AI Layer**: GenAI via Python Flask API using Together AI (Mistral-7B-Instruct/LLaMA3)
- **Integration**: REST API calls from CAP to Flask
- **Development Environment**: SAP Business Application Studio (BAS)

## 📁 Project Structure
```
demand-supply-reconciliation-system/
├── db/                          # Database artifacts (CDS models, HDI)
│   ├── data-model.cds          # Core data models
│   ├── views.cds               # Database views
│   └── csv/                    # Sample data files
├── srv/                        # CAP services
│   ├── reconciliation-service.cds  # Service definitions
│   ├── reconciliation-service.js   # Service implementations
│   ├── analytics-service.cds
│   ├── analytics-service.js
│   ├── admin-service.cds
│   └── admin-service.js
├── app/                        # UI5 applications
│   └── reconciliation-dashboard/  # Main Fiori dashboard
├── genai-service/              # Python Flask GenAI API
│   ├── app.py                  # Flask application
│   ├── requirements.txt        # Python dependencies
│   └── models/                 # AI model configurations
├── test/                       # Test files
└── package.json               # Node.js dependencies
```

## 🚀 Core Features
1. **Real-time Monitoring**: Demand vs Supply tracking across plants and vendors
2. **Intelligent Reconciliation**: GenAI-powered mismatch resolution suggestions
3. **Auto-Reconciliation Workflow**: Automated stock reallocation, procurement, and production
4. **Approval System**: Business user approval/rejection of AI suggestions
5. **Analytics Dashboard**: Comprehensive reporting and history tracking

## 🛠️ Development Setup
1. Open in SAP Business Application Studio
2. Install dependencies: `npm install`
3. Build the project: `npm run build`
4. Deploy to HANA Cloud: `npm run deploy`
5. Start development server: `npm run watch`

## 📊 Data Models
- **Master Data**: Plants, Vendors, Products, Stock
- **Transactional Data**: Demand, Supply, ReconciliationResults
- **AI Integration**: GenAIRecommendations, ProcurementRequests, ProductionOrders
- **Workflow**: StockReallocations, ApprovalHistory

## 🤖 GenAI Integration
The system integrates with Together AI models (Mistral-7B-Instruct/LLaMA3) to:
- Analyze demand-supply mismatches
- Generate contextual recommendations
- Suggest optimal resolution strategies
- Learn from historical decisions

## 🔐 Security
- XSUAA authentication and authorization
- Role-based access control (Admin, Planner, Supply Manager, Approver)
- Plant and region-based data isolation
