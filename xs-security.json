{"xsappname": "demand-supply-reconciliation-system", "tenant-mode": "dedicated", "description": "Security descriptor for Demand-Supply Reconciliation System", "scopes": [{"name": "uaa.user", "description": "UAA"}, {"name": "$XSAPPNAME.Admin", "description": "Admin access to all services"}, {"name": "$XSAPPNAME.PlannerRead", "description": "Read access for demand planners"}, {"name": "$XSAPPNAME.PlannerWrite", "description": "Write access for demand planners"}, {"name": "$XSAPPNAME.SupplyManagerRead", "description": "Read access for supply managers"}, {"name": "$XSAPPNAME.SupplyManagerWrite", "description": "Write access for supply managers"}, {"name": "$XSAPPNAME.ApproverRead", "description": "Read access for approvers"}, {"name": "$XSAPPNAME.ApproverWrite", "description": "Approval access for reconciliation decisions"}], "attributes": [{"name": "Plant", "description": "Plant assignment for users", "valueType": "string"}, {"name": "Region", "description": "Region assignment for users", "valueType": "string"}], "role-templates": [{"name": "Admin", "description": "System Administrator", "scope-references": ["uaa.user", "$XSAPPNAME.Admin"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Demand Planning User", "scope-references": ["uaa.user", "$XSAPPNAME.PlannerRead", "$XSAPPNAME.PlannerWrite"]}, {"name": "SupplyManager", "description": "Supply Management User", "scope-references": ["uaa.user", "$XSAPPNAME.SupplyManagerRead", "$XSAPPNAME.SupplyManagerWrite"]}, {"name": "Approver", "description": "Reconciliation Approver", "scope-references": ["uaa.user", "$XSAPPNAME.ApproverRead", "$XSAPPNAME.ApproverWrite"]}]}