namespace com.sap.manufacturing.reconciliation;

using {
    Currency,
    managed,
    cuid,
    temporal
} from '@sap/cds/common';

// Master Data Entities

entity Plants : cuid, managed {
    plantCode     : String(10) not null;
    plantName     : String(100) not null;
    region        : String(50);
    country       : String(50);
    address       : String(500);
    isActive      : Boolean default true;
    capacity      : Decimal(15,2);
    
    // Relationships
    stocks        : Association to many Stocks on stocks.plant = $self;
    demands       : Association to many Demands on demands.plant = $self;
    supplies      : Association to many Supplies on supplies.plant = $self;
    productionOrders : Association to many ProductionOrders on productionOrders.plant = $self;
}

entity Vendors : cuid, managed {
    vendorCode    : String(10) not null;
    vendorName    : String(100) not null;
    contactPerson : String(100);
    email         : String(100);
    phone         : String(20);
    address       : String(500);
    region        : String(50);
    country       : String(50);
    isActive      : Boolean default true;
    rating        : Decimal(3,2); // 0.00 to 5.00
    leadTimeDays  : Integer;
    
    // Relationships
    supplies      : Association to many Supplies on supplies.vendor = $self;
    procurementRequests : Association to many ProcurementRequests on procurementRequests.vendor = $self;
}

entity Products : cuid, managed {
    productCode   : String(20) not null;
    productName   : String(100) not null;
    description   : String(500);
    category      : String(50);
    unitOfMeasure : String(10);
    standardCost  : Decimal(15,2);
    currency      : Currency;
    isActive      : Boolean default true;
    minStockLevel : Decimal(15,2);
    maxStockLevel : Decimal(15,2);
    leadTimeDays  : Integer;
    
    // Relationships
    stocks        : Association to many Stocks on stocks.product = $self;
    demands       : Association to many Demands on demands.product = $self;
    supplies      : Association to many Supplies on supplies.product = $self;
    procurementRequests : Association to many ProcurementRequests on procurementRequests.product = $self;
    productionOrders : Association to many ProductionOrders on productionOrders.product = $self;
}

// Transactional Data Entities

entity Stocks : cuid, managed {
    plant         : Association to Plants not null;
    product       : Association to Products not null;
    currentStock  : Decimal(15,2) not null default 0;
    reservedStock : Decimal(15,2) default 0;
    availableStock : Decimal(15,2) default 0;
    lastUpdated   : Timestamp;
    
    // Relationships
    reallocations : Association to many StockReallocations on reallocations.fromStock = $self;
}

entity Demands : cuid, managed {
    plant         : Association to Plants not null;
    product       : Association to Products not null;
    demandDate    : Date not null;
    requiredQuantity : Decimal(15,2) not null;
    priority      : String(10) default 'MEDIUM'; // HIGH, MEDIUM, LOW
    status        : String(20) default 'OPEN'; // OPEN, PARTIALLY_FULFILLED, FULFILLED, CANCELLED
    customerOrder : String(50);
    dueDate       : Date;
    
    // Relationships
    reconciliationResults : Association to many ReconciliationResults on reconciliationResults.demand = $self;
}

entity Supplies : cuid, managed {
    plant         : Association to Plants;
    vendor        : Association to Vendors;
    product       : Association to Products not null;
    supplyDate    : Date not null;
    availableQuantity : Decimal(15,2) not null;
    confirmedQuantity : Decimal(15,2) default 0;
    status        : String(20) default 'PLANNED'; // PLANNED, CONFIRMED, IN_TRANSIT, DELIVERED, CANCELLED
    purchaseOrder : String(50);
    deliveryDate  : Date;
    
    // Relationships
    reconciliationResults : Association to many ReconciliationResults on reconciliationResults.supply = $self;
}

// Reconciliation and AI Entities

entity ReconciliationResults : cuid, managed {
    demand        : Association to Demands;
    supply        : Association to Supplies;
    plant         : Association to Plants not null;
    product       : Association to Products not null;
    reconciliationDate : Date not null;
    demandQuantity : Decimal(15,2);
    supplyQuantity : Decimal(15,2);
    shortfall     : Decimal(15,2);
    surplus       : Decimal(15,2);
    status        : String(20) default 'PENDING'; // PENDING, RESOLVED, ESCALATED
    resolutionType : String(30); // STOCK_REALLOCATION, PROCUREMENT, PRODUCTION, MANUAL
    priority      : String(10) default 'MEDIUM';
    
    // Relationships
    genAIRecommendations : Composition of many GenAIRecommendations on genAIRecommendations.reconciliationResult = $self;
    procurementRequests : Association to many ProcurementRequests on procurementRequests.reconciliationResult = $self;
    productionOrders : Association to many ProductionOrders on productionOrders.reconciliationResult = $self;
    stockReallocations : Association to many StockReallocations on stockReallocations.reconciliationResult = $self;
}

entity GenAIRecommendations : cuid, managed {
    reconciliationResult : Association to ReconciliationResults not null;
    aiModel       : String(50); // MISTRAL_7B, LLAMA3, etc.
    recommendation : LargeString not null;
    confidence    : Decimal(5,2); // 0.00 to 100.00
    reasoning     : LargeString;
    suggestedActions : LargeString;
    estimatedCost : Decimal(15,2);
    estimatedTime : Integer; // in hours
    status        : String(20) default 'PENDING'; // PENDING, APPROVED, REJECTED, IMPLEMENTED
    approvedBy    : String(100);
    approvedAt    : Timestamp;
    implementedAt : Timestamp;
    feedback      : LargeString;
    
    // JSON fields for structured data
    actionPlan    : LargeString; // JSON structure
    riskAssessment : LargeString; // JSON structure
}

entity ProcurementRequests : cuid, managed {
    reconciliationResult : Association to ReconciliationResults;
    vendor        : Association to Vendors not null;
    product       : Association to Products not null;
    requestedQuantity : Decimal(15,2) not null;
    urgency       : String(10) default 'NORMAL'; // URGENT, HIGH, NORMAL, LOW
    requiredDate  : Date not null;
    estimatedCost : Decimal(15,2);
    currency      : Currency;
    status        : String(20) default 'DRAFT'; // DRAFT, SUBMITTED, APPROVED, ORDERED, DELIVERED, CANCELLED
    approvedBy    : String(100);
    approvedAt    : Timestamp;
    purchaseOrder : String(50);
    
    // AI-generated fields
    aiGenerated   : Boolean default false;
    aiConfidence  : Decimal(5,2);
}

entity ProductionOrders : cuid, managed {
    reconciliationResult : Association to ReconciliationResults;
    plant         : Association to Plants not null;
    product       : Association to Products not null;
    plannedQuantity : Decimal(15,2) not null;
    actualQuantity : Decimal(15,2) default 0;
    plannedStartDate : Date not null;
    plannedEndDate : Date not null;
    actualStartDate : Date;
    actualEndDate : Date;
    priority      : String(10) default 'NORMAL';
    status        : String(20) default 'PLANNED'; // PLANNED, RELEASED, IN_PROGRESS, COMPLETED, CANCELLED
    estimatedCost : Decimal(15,2);
    actualCost    : Decimal(15,2);
    
    // AI-generated fields
    aiGenerated   : Boolean default false;
    aiConfidence  : Decimal(5,2);
}

entity StockReallocations : cuid, managed {
    reconciliationResult : Association to ReconciliationResults;
    fromStock     : Association to Stocks not null;
    toPlant       : Association to Plants not null;
    product       : Association to Products not null;
    reallocatedQuantity : Decimal(15,2) not null;
    plannedDate   : Date not null;
    actualDate    : Date;
    status        : String(20) default 'PLANNED'; // PLANNED, IN_TRANSIT, COMPLETED, CANCELLED
    transportCost : Decimal(15,2);
    estimatedDays : Integer;
    
    // AI-generated fields
    aiGenerated   : Boolean default false;
    aiConfidence  : Decimal(5,2);
}

// Audit and History Entities

entity ApprovalHistory : cuid, managed {
    entityType    : String(50) not null; // GENAI_RECOMMENDATION, PROCUREMENT_REQUEST, etc.
    entityID      : UUID not null;
    action        : String(20) not null; // APPROVED, REJECTED, MODIFIED
    approver      : String(100) not null;
    approvalDate  : Timestamp not null;
    comments      : LargeString;
    previousStatus : String(20);
    newStatus     : String(20);
}
